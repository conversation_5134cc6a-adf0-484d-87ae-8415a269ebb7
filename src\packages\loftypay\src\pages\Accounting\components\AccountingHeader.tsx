import { ReactNode, useState, useEffect } from "react";
import { useLocation, useNavigate } from "react-router";
import { useTranslation } from "react-i18next";

import { Box, Grid, Tab, Tabs, Typography, Theme } from "@mui/material";

// @ts-ignore
import { Translation } from "i18next";
import { useFlag } from "@unleash/proxy-client-react";

import { makeStyles } from "@mui/styles";
import { useOrganisationData } from "@rentancy/common";

type AccountingTabs = {
  labelKey: keyof Translation;
  value: string;
  disabled?: boolean;
};

const DEPOSIT_ROUTE = {
  labelKey: "depositManagement",
  value: "/accounting/deposit-management",
};
const initAccountingTabs = (approvalsFlag: boolean): AccountingTabs[] => [
  {
    labelKey: "charges",
    value: "/accounting/invoices",
  },
  {
    labelKey: "invoices",
    value: "/accounting/bills",
  },
  {
    labelKey: "bank",
    value: "/accounting/bank",
  },
  {
    labelKey: "ledgerBalances",
    value: "/accounting/ledger",
  },
  {
    labelKey: "suspendedTransactions",
    value: "/accounting/suspended",
  },
  {
    labelKey: "payout",
    value: "/accounting/payouts",
  },
  {
    labelKey: "arrears",
    value: "/accounting/arrears",
  },
  {
    labelKey: "approvals",
    value: "/accounting/approvals",
    disabled: !approvalsFlag,
  },
  {
    labelKey: "paymentsHistory",
    value: "/accounting/payments-history",
    disabled: !approvalsFlag,
  },
];

interface Props {
  addNewBtn: () => ReactNode;
}

export const AccountingHeader = ({ addNewBtn }: Props) => {
  const approvalsFlag = useFlag("approvals");
  const { organisation } = useOrganisationData();
  const [accountingTabs, setAccountingTabs] = useState<AccountingTabs[]>(
    initAccountingTabs(approvalsFlag),
  );
  const classes = useStyles();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation("loftyPay");
  const isDepositForLoftyPayAvailable = useFlag("DepositForLoftyPay");

  const goTo = (path: string) => {
    navigate(path);
  };

  useEffect(() => {
    const newAccountingTabs: AccountingTabs[] = [
      ...initAccountingTabs(approvalsFlag),
      ...(isDepositForLoftyPayAvailable ? [DEPOSIT_ROUTE] : []),
    ];
    if (organisation?.type === "LANDLORD") {
      setAccountingTabs(newAccountingTabs.filter(({ labelKey }) => labelKey !== "payout"));
    } else {
      setAccountingTabs(newAccountingTabs);
    }
  }, [organisation?.type, isDepositForLoftyPayAvailable]);

  return (
    <Box className={classes.wrapper}>
      <Grid container justifyContent="space-between" pt={3} pb={2} height={76.5}>
        <Grid item md={8}>
          <Typography variant="h2" color="text.primary">
            {t("accounting")}
          </Typography>
        </Grid>
        <Grid item justifyContent="flex-end">
          {addNewBtn()}
        </Grid>
      </Grid>
      <Grid container direction="row" justifyContent="space-between">
        <Grid item>
          <Tabs
            value={location.state ? location.state.background.pathname : location.pathname}
            indicatorColor="primary"
            variant="scrollable"
            textColor="primary"
          >
            {accountingTabs
              .filter(tab => !tab.disabled)
              .map(tab => (
                <Tab
                  key={tab.value}
                  className={classes.tab}
                  onClick={() => goTo(tab.value)}
                  label={t(tab.labelKey as string)}
                  value={tab.value}
                />
              ))}
          </Tabs>
        </Grid>
      </Grid>
    </Box>
  );
};

const useStyles = makeStyles((theme: Theme) => ({
  wrapper: {
    padding: theme.spacing(0, 2, 0, 4),
    borderBottom: `solid 1px ${theme.palette.specialColors.themeBorder}`,
    background: theme.palette.common.white,
  },
  tab: {
    minWidth: 90,
    padding: theme.spacing(0, 1),
  },
}));
