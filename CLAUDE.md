# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Repository Overview

A monorepo for the Rentancy property management platform with regional apps for UK and US markets, built using React, TypeScript, and AWS Amplify.

## Architecture & Structure

This is a **Lerna monorepo** with the following structure:

- **Apps**: Regional applications in `src/apps/`
  - `rentancy-uk`: UK-specific application
  - `rentancy-us`: US-specific application
- **Packages**: Shared libraries in `src/packages/`
  - `@rentancy/common`: Common utilities and shared code
  - `@rentancy/icon`: SVG icon library and sprite generation
  - `@rentancy/ui`: UI component library
  - `@rentancy/chatbot`: Chatbot functionality
  - `@rentancy/loftypay`: Payment integration

## Development Commands

### Setup
```bash
# Install all dependencies
yarn install

# Build shared packages (required before running apps)
yarn build:packages
```

### Running Applications

#### Rentancy UK
```bash
cd src/apps/rentancy-uk
yarn prestart        # Build dependencies
yarn start           # Start dev server
yarn start:devuk     # Start with dev.uk environment
yarn start:stageuk   # Start with stage.uk environment
```

#### Rentancy US
```bash
cd src/apps/rentancy-us
yarn prestart        # Build dependencies
yarn start           # Start dev server
```

### Testing & Quality
```bash
# Run tests for UK app
yarn test

# Run linting
yarn lint
yarn lint:fix

# Format code
yarn format
```

### Build Commands
```bash
# Build UK app
cd src/apps/rentancy-uk
yarn build

# Build US app
cd src/apps/rentancy-us
yarn build
```

## Technology Stack

- **Frontend**: React 18, TypeScript, Vite
- **UI Framework**: Material-UI (MUI)
- **State Management**: React Context, Formik
- **Backend**: AWS Amplify, AWS AppSync (GraphQL)
- **Authentication**: AWS Cognito
- **Database**: DynamoDB, S3 storage
- **Testing**: Jest, React Testing Library
- **Linting**: ESLint with custom config
- **Formatting**: Prettier

## Environment Configuration

### AWS Environments
| Environment | Branch | URL |
|-------------|--------|-----|
| Development | `dev` | https://dev.uk.loftyworks.com |
| Staging | `epic-branch` | https://stage.uk.loftyworks.com |
| Production | `master` | https://app.loftyworks.com |

### Environment Variables
- Environment files are located in respective app directories
- AWS Amplify configuration is automatically injected via `scripts/init-aws-envs.js`
- Regional configurations support UK/EU and US deployments

## Directory Structure

```
src/
├── apps/
│   ├── rentancy-uk/          # UK application
│   └── rentancy-us/          # US application
├── packages/
│   ├── common/               # Shared utilities
│   ├── icon/                 # Icon library
│   ├── ui/                   # UI components
│   ├── chatbot/              # Chatbot features
│   └── loftypay/             # Payment features
├── @Types/                   # Global type definitions
└── aws-exports.js           # AWS Amplify configuration
```

## Key Concepts

### Multi-tenancy
- Applications support multiple organizations (workspaces)
- Data isolation at the organization level
- Role-based access control (RBAC)

### Regional Features
- **UK**: Rightmove integration, UK-specific compliance
- **US**: Zillow integration, US-specific compliance
- **Common**: Core property management functionality

### AWS Services Used
- **Amplify**: Hosting, authentication, API
- **AppSync**: GraphQL API
- **DynamoDB**: Database
- **S3**: File storage
- **Cognito**: User authentication
- **CloudFormation**: Infrastructure as code

## Development Workflow

1. **Setup**: `yarn install && yarn build:packages`
2. **Development**: Use appropriate start command for region
3. **Testing**: Run `yarn test` in app directory
4. **Linting**: Run `yarn lint` and `yarn lint:fix`
5. **Build**: Use `yarn build` for production builds
6. **Deploy**: GitHub Actions handles deployment based on branch

## Package-specific Notes

### @rentancy/icon
- SVG icon library with sprite generation
- Build with `yarn build` in package directory
- Preview icons with `yarn preview:icons`

### @rentancy/common
- Shared utilities and helpers
- Must be built before running applications
- Peer dependencies for AWS services

## Troubleshooting

- **Build failures**: Ensure all packages are built first
- **Missing dependencies**: Run `yarn install` at root
- **Environment issues**: Check AWS Amplify configuration
- **Type errors**: Run `yarn type-check` in app directory