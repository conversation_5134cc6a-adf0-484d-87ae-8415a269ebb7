import { FC, useEffect, useMemo } from "react";

import { Box, Theme } from "@mui/material";
import { useTranslation } from "react-i18next";
import { makeStyles } from "@mui/styles";
import { HomePageSearchBox } from "Components/HomePage/HomePageSearchBox";

import { useQueryString, useApi, useDeviceScreen, useGlobal, useUser } from "@rentancy/common";
import { PageHeader } from "Components/PageHeader/PageHeader";
import { EndOfTenancyDesktop } from "./EndOfTenancyDesktop";
import { getAllEndOfTenancy } from "Api/EndOfTenancy/index";
import { MenuDrawer } from "Components/HomePage/Drawer";

export const EndOfTenancyList: FC = () => {
  const { t } = useTranslation();
  const classes = useStyles();
  const { isDesktop } = useDeviceScreen();
  const { apiCall: getEOTApi, loading, data, refetch } = useApi(getAllEndOfTenancy);
  const { user } = useUser();
  const isLandlordWorkspace = useMemo(() => user.organisation?.type === "LANDLORD", [user]);
  const {
    globalState: { showSidebar },
    dispatch,
  } = useGlobal();

  const setShowSideBar = (show: boolean) => {
    dispatch({
      type: "SET_SHOW_SIDEBAR",
      value: show,
    });
  };

  const { query } = useQueryString<{
    type?: number;
    page: string;
    limit: string;
    q: string;
    fromEndDate: string;
    toEndDate: string;
    stage: string;
  }>();

  useEffect(() => {
    const input = {
      type: query.type || (isLandlordWorkspace ? "ACTIVE" : "ALL"),
      limit: isNaN(+query.limit) ? 30 : +query.limit,
      page: isNaN(+query.page) ? 1 : +query.page,
      keyword: query.q,
      toEndDate: query.toEndDate,
      fromEndDate: query.fromEndDate,
      stage: query.stage,
    };
    getEOTApi(input as any);
  }, [
    query.type,
    query.limit,
    query.page,
    query.q,
    query.fromEndDate,
    query.toEndDate,
    query.stage,
  ]);

  return (
    <Box className={classes.container}>
      {!isDesktop && (
        <Box>
          <HomePageSearchBox
            className={classes.mobileHeader}
            handleOpenMenu={() => setShowSideBar(true)}
            rootStyles={{ minHeight: "75px" }}
          />
          <MenuDrawer
            open={showSidebar}
            handleClose={() => {
              setShowSideBar(false);
            }}
            activeMenu={"END_OF_TENANCY"}
          />
        </Box>
      )}
      <PageHeader
        htmlTitle={t("endOfTenancy")}
        pageTitle={t("endOfTenancy")}
        buttons={<></>}
        tabs={<> </>}
      />
      <EndOfTenancyDesktop data={data?.data} loading={loading} refetch={refetch} />
    </Box>
  );
};

const useStyles = makeStyles((theme: Theme) => ({
  container: {
    background: theme.palette.specialColors.pageBackground,
    width: "100%",
    flex: 1,
    display: "flex",
    flexDirection: "column",
    [theme.breakpoints.down("lg")]: {
      overflow: "scroll",
      position: "absolute",
    },
  },
  mobileHeader: {
    position: "relative",
    marginTop: "0 !important",
  },
}));
