import { fetchHelper } from "@rentancy/common";
import type { Result } from "Api/type";
import { STATUS_OPTIONS } from "Containers/Contracts/EndOfTenancy/EndOfTenancyList/const";
import type {
  EndOfTenancyItem,
  EndOfTenancyListItem,
  ConfirmationEmailInfo,
  ConfirmationEmailRequest,
  ConfirmationEmailResponse,
  MixedLetter,
} from "Containers/Contracts/EndOfTenancy/types";

type StageKey = keyof typeof STATUS_OPTIONS;

export const getAllEndOfTenancy = ({
  fromEndDate = "",
  toEndDate = "",
  page = 1,
  limit: size = 30,
  keyword,
  stage,
}: {
  fromEndDate: string | null;
  toEndDate: string | null;
  page: number | null;
  limit: number | null;
  keyword: string | null;
  stage: StageKey | null;
}): Promise<Result<EndOfTenancyListItem[]>> => {
  const searchParams = new URLSearchParams();

  if (fromEndDate) {
    searchParams.set("fromEndDate", fromEndDate.toString());
  }

  if (toEndDate) {
    searchParams.set("toEndDate", toEndDate.toString());
  }

  if (page) {
    searchParams.set("page", page?.toString());
  }

  if (size) {
    searchParams.set("size", size?.toString());
  }

  if (keyword) {
    searchParams.set("keyword", keyword?.toString());
  }

  if (stage) {
    searchParams.set("stage", STATUS_OPTIONS[stage]);
  }
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/endOfTenancy/page?${searchParams.toString()}&t=${Date.now()}`,
    convertJson: true,
    method: "GET",
  });
};

export const getEndofTenancyDetail = (id: string): Promise<Result<EndOfTenancyItem>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/endOfTenancy?eotId=${id}`,
    convertJson: true,
    method: "GET",
  });
};

export const getEotDetailByTenancyId = (tenancyId: string): Promise<Result<EndOfTenancyItem>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/endOfTenancy?tenancyId=${tenancyId}`,
    convertJson: true,
    method: "GET",
  });
};

export const updateEndofTenancyDetail = ({
  id,
  data,
}: {
  id: string;
  data: EndOfTenancyItem;
}): Promise<Result<EndOfTenancyItem>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/endOfTenancy?eotId=${id}`,
    convertJson: true,
    method: "PUT",
    body: data,
  });
};

export const getPreviewedEmail = ({
  eotId,
  letter,
}: {
  eotId: string;
  letter: MixedLetter;
}): Promise<Result<MixedLetter>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/v1/endOfTenancy/previewedEmail`,
    convertJson: true,
    method: "POST",
    body: {
      eotId,
      letter,
    },
  });
};

export const updateConfirmationEmail = (
  data: ConfirmationEmailInfo,
): Promise<Result<ConfirmationEmailResponse>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/public/eot-email-confirm`,
    convertJson: true,
    method: "POST",
    body: data,
  });
};

export const getConfirmationEmail = (
  params: ConfirmationEmailRequest,
): Promise<Result<ConfirmationEmailResponse>> => {
  return fetchHelper({
    url: `${process.env.MAIN_ENDPOINT}/api/public/eot-email-confirm?token=${params.token}`,
    convertJson: true,
    method: "GET",
  });
};
