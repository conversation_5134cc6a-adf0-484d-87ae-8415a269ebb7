import { Box, Grid, Table, TableBody, TableContainer, Theme } from "@mui/material";
import {
  type Document,
  // updateDocument,
  newUpdateDocument,
  useApi,
  useQueryString,
  DEFAULT_PAGINATION_LIMIT,
  type OpenConfirmDeletion,
  type OpenRenameModal,
  type WarningModalAction,
  DocumentQueryType,
} from "@rentancy/common";
import { Pagination } from "@rentancy/ui";
import { makeStyles } from "@mui/styles";
import { useContext, useEffect, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { clsx } from "clsx";
import { useLayoutStyles, useTableStyles } from "@rentancy/ui";
import { DocumentsTableHeader } from "./DocumentsTableHeader";
import { DocumentsTableRow } from "./DocumentsTableRow";
import { DocumentsTableRowSkeletonLoading } from "./DocumentsTableRowSkeletonLoading";
import { EmptyBackground } from "@rentancy/ui";
import { DocumentContext } from "../../DocumentsProvider";
import { useTranslation } from "react-i18next";
import { PopupModals } from "../../../../Components/Documents/PopupModals";

export const DocumentsTable = () => {
  const layoutClasses = useLayoutStyles();
  const tableClasses = useTableStyles();
  const [pageCount, setPageCount] = useState<number>(0);
  const [dataToShow, setDataToShow] = useState<Document[] | null>(null);

  const { query } = useQueryString<DocumentQueryType>();

  const [openConfirmDeletion, setOpenConfirmDeletion] = useState<OpenConfirmDeletion | null>(null);

  const [openRenameModal, setOpenRenameModal] = useState<OpenRenameModal | null>(null);

  const [warningModalAction, setWarningModalAction] = useState<WarningModalAction | null>(null);

  const location = useLocation();
  const container = useRef<HTMLDivElement>(null);

  const {
    toShowDocuments,
    loading,
    selectedTab,
    getData: refreshData,
    total,
  } = useContext(DocumentContext);

  const { t } = useTranslation();

  const { apiCall: updateDocumentApi } = useApi(newUpdateDocument, {
    successMessage: t("documentUpdatedSuccessfully"),
  });

  const { refreshDocumentCounts } = useContext(DocumentContext);

  const updateDocumentItem = async (props: Partial<Document>, id: string) => {
    await updateDocumentApi({ ...props, id }, { followingFunctions: refreshData });
    if (refreshDocumentCounts) {
      refreshDocumentCounts();
    }
    setWarningModalAction(null);
  };

  useEffect(() => {
    container.current?.scrollTo(0, 0);
  }, [query]);

  useEffect(() => {
    if (location.state?.reload) {
      refreshData();
    }
  }, [location.state?.reload, refreshData]);

  useEffect(() => {
    setDataToShow(toShowDocuments);
    setPageCount(Math.ceil(total / (query.limit || 30)));
  }, [total, query.page, query.limit, toShowDocuments, query.orderBy, query.orderDirection]);

  const classes = useStyles();

  return (
    <Grid className={clsx(classes.root, layoutClasses.flexColumn, layoutClasses.fullH)}>
      <TableContainer
        ref={container}
        className={clsx(
          layoutClasses.flexFull,
          layoutClasses.positionRelative,
          tableClasses.tableContainer,
          classes.table,
        )}
      >
        <Table aria-label="simple table" stickyHeader>
          <DocumentsTableHeader selectedTab={selectedTab} />
          <TableBody>
            {loading && (
              <>
                <DocumentsTableRowSkeletonLoading />
                <DocumentsTableRowSkeletonLoading />
                <DocumentsTableRowSkeletonLoading style={{ opacity: 0.8 }} />
                <DocumentsTableRowSkeletonLoading style={{ opacity: 0.5 }} />
                <DocumentsTableRowSkeletonLoading style={{ opacity: 0.2 }} />
                <DocumentsTableRowSkeletonLoading style={{ opacity: 0.2 }} />
                <DocumentsTableRowSkeletonLoading style={{ opacity: 0.2 }} />
              </>
            )}
            {!loading && pageCount === 0 && <EmptyBackground />}
            {!loading &&
              dataToShow &&
              dataToShow.map(document => (
                <DocumentsTableRow
                  key={document.id}
                  document={document}
                  selectedTab={selectedTab}
                  updateDocumentItem={updateDocumentItem}
                  setWarningModalAction={setWarningModalAction}
                  setOpenConfirmDeletion={setOpenConfirmDeletion}
                  setOpenRenameModal={setOpenRenameModal}
                />
              ))}
            <PopupModals
              warningModalAction={warningModalAction}
              setWarningModalAction={setWarningModalAction}
              updateDocumentItem={updateDocumentItem}
              openConfirmDeletion={openConfirmDeletion}
              setOpenConfirmDeletion={setOpenConfirmDeletion}
              openRenameModal={openRenameModal}
              setOpenRenameModal={setOpenRenameModal}
            />
          </TableBody>
        </Table>
      </TableContainer>
      <Box width="100%" className={tableClasses.paginationWrapper}>
        <Pagination count={pageCount} disabled={false} paginationLimit={DEFAULT_PAGINATION_LIMIT} />
      </Box>
    </Grid>
  );
};

const useStyles = makeStyles<Theme>(theme => ({
  root: { padding: theme.spacing(0, 0, 7), flexGrow: 1 },
  table: {
    border: "0px !important",
    paddingLeft: 15,
  },
}));
