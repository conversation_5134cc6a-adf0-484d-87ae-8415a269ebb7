import { FC, useContext } from "react";
import { Box, Tab, Tabs, styled } from "@mui/material";
import { useTranslation } from "react-i18next";
import { DocumentContext } from "../DocumentsProvider";
import { DocumentTabs as DocumentTabsEnum } from "./DocumentsHeader";

const StyledTabs = styled(Tabs)(({ theme }) => ({
  minHeight: "auto",
  "& .MuiTabs-indicator": {
    display: "none",
  },
  "& .MuiTabs-flexContainer": {
    gap: theme.spacing(1),
  },
}));

const StyledTab = styled(Tab)(({ theme }) => ({
  minHeight: "auto",
  minWidth: "auto",
  padding: theme.spacing(1, 2),
  borderRadius: theme.spacing(0.75),
  color: theme.palette.text.secondary,
  fontSize: "0.875rem",
  fontWeight: 500,
  textTransform: "none",
  transition: "all 0.2s ease",
  "&.Mui-selected": {
    backgroundColor: theme.palette.grey[100],
    color: theme.palette.grey[800],
  },
  "&:hover": {
    backgroundColor: theme.palette.grey[200],
  },
}));

export const DocumentTabsComponent: FC = () => {
  const { t } = useTranslation();
  const {
    selectedTab,
    setSelectedTab,
    documentsCounts,
    setSearchPhrase,
    setDocumentCategory,
    setPropertyFilter,
    setDayFilter,
    setQuery,
  } = useContext(DocumentContext);

  const getTabLabel = (tab: DocumentTabsEnum, count?: number) => {
    let label = "";
    switch (tab) {
      case DocumentTabsEnum.ALL:
        label = t("allDocuments");
        break;
      case DocumentTabsEnum.EXPIRING:
        label = t("expiring");
        break;
      case DocumentTabsEnum.ARCHIVE:
        label = t("archive");
        break;
      default:
        label = t("allDocuments");
    }
    return count !== undefined ? `${label} ${count}` : label;
  };

  const handleChange = (event: React.SyntheticEvent, newValue: DocumentTabsEnum) => {
    setSelectedTab(newValue);
    // Clear filters when switching tabs
    setSearchPhrase("");
    setDocumentCategory("");
    setPropertyFilter([]);
    setDayFilter(30);
    // Reset pagination parameters
    if (setQuery) {
      setQuery({ page: 1, limit: 30 });
    }
  };

  const getCounts = (tab: DocumentTabsEnum) => {
    return documentsCounts[tab] || 0;
  };

  return (
    <Box>
      <StyledTabs value={selectedTab} onChange={handleChange} variant="standard">
        <StyledTab
          label={getTabLabel(DocumentTabsEnum.ALL, getCounts(DocumentTabsEnum.ALL))}
          value={DocumentTabsEnum.ALL}
        />
        <StyledTab
          label={getTabLabel(DocumentTabsEnum.EXPIRING, getCounts(DocumentTabsEnum.EXPIRING))}
          value={DocumentTabsEnum.EXPIRING}
        />
        <StyledTab
          label={getTabLabel(DocumentTabsEnum.ARCHIVE, getCounts(DocumentTabsEnum.ARCHIVE))}
          value={DocumentTabsEnum.ARCHIVE}
        />
      </StyledTabs>
    </Box>
  );
};
