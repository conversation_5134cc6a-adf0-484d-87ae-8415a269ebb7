import moment from "moment";
import { <PERSON>rid, TableCell, TableRow, Typography, TextField, useTheme, Theme } from "@mui/material";
import { Checkbox } from "@rentancy/ui";
import {
  curtailText,
  Document,
  updateDocument,
  useApi,
  DocumentStatusSelect,
  DocumentContext,
  type UpdateDocument,
  type WarningModalAction,
  type OpenConfirmDeletion,
  type OpenRenameModal,
} from "@rentancy/common";
import { DocumentItemPermissions } from "../../../../Components/Documents/DocumentItem/DocumentItemPermissions";
import { FileIcon, defaultStyles, DefaultExtensionType } from "react-file-icon";
import { makeStyles } from "@mui/styles";
import { useState, useContext, useEffect } from "react";
import { useTranslation } from "react-i18next";
import { DocumentTabs } from "../DocumentsHeader";
import { DocumentPopupMenu } from "../../../../Components/Documents/DocumentPopupMenu";
import { DesktopDatePicker } from "@mui/x-date-pickers";

type Props = {
  document: Document;
  selectedTab: DocumentTabs;
  updateDocumentItem: (props: Partial<UpdateDocument>, id: string) => void;
  setWarningModalAction: (props: WarningModalAction) => void;
  setOpenConfirmDeletion: (props: OpenConfirmDeletion) => void;
  setOpenRenameModal: (props: OpenRenameModal) => void;
};

export const DocumentsTableRow = ({
  document,
  selectedTab,
  updateDocumentItem,
  setWarningModalAction,
  setOpenConfirmDeletion,
  setOpenRenameModal,
}: Props) => {
  const documentFormat = document.mimeType?.split("/")[1] || "";
  const classes = useStyles();

  const { apiCall: updateDocumentApi } = useApi(updateDocument);
  const { documentTypes, refreshDocumentCounts } = useContext(DocumentContext);

  const [done, setDone] = useState<boolean>(document.done ?? false);
  const [loadingSetDone, setLoadingSetDone] = useState<boolean>(false);
  const [anchorDateEl, setAnchorDateEl] = useState<HTMLElement | null>(null);
  const [dateToChange, setDateToChange] = useState<string | null>(null);
  const [newChosenYear, setNewChosenYear] = useState<number | null>(null);

  useEffect(() => {
    setDateToChange(document?.expiry || "");
  }, [document?.expiry]);

  const { t } = useTranslation();

  const theme = useTheme();

  const changeDoneCheckBox = async () => {
    setLoadingSetDone(true);
    await updateDocumentApi({
      done: document.done ? !document.done : true,
      id: document.id,
    });

    if (refreshDocumentCounts) {
      refreshDocumentCounts();
    }
    setDone(!done);
    setLoadingSetDone(false);
  };

  function getMonthAndDay(dateString: string) {
    const date = new Date(dateString);
    const month = date.getMonth() + 1;
    const day = date.getDate();
    return { month, day };
  }

  return done && document.daysLeft && document.daysLeft < 0 ? (
    <></>
  ) : (
    <TableRow hover className={classes.link}>
      <TableCell
        className={classes.tableCell}
        sx={{ maxWidth: 105, width: "20%" }}
        onClick={e => {
          e.preventDefault();
        }}
      >
        <Grid
          container
          alignItems="center"
          spacing={1}
          sx={{ marginTop: 0 }}
          className={classes.fileCell}
        >
          <div className={classes.fileIcon}>
            <FileIcon
              extension={documentFormat}
              {...defaultStyles[documentFormat as DefaultExtensionType]}
            />
          </div>
          <Typography noWrap variant="body2">
            {curtailText(document.name, 30)}
          </Typography>
        </Grid>
      </TableCell>
      <TableCell className={classes.tableCell} sx={{ maxWidth: 180 }}>
        <Typography variant="body2" noWrap>
          {document.property?.addressLine1 ?? "-"}
        </Typography>
      </TableCell>
      <TableCell className={classes.tableCell} sx={{ width: "15%" }}>
        <Typography variant="body2" noWrap sx={{ maxWidth: 150 }}>
          {documentTypes.find(item => document.type === item.value)?.text ?? "-"}
        </Typography>
      </TableCell>
      <TableCell className={classes.tableCell} sx={{ width: "10%" }}>
        <div onClick={e => setAnchorDateEl(e.currentTarget)}>
          {document.expiry ? (
            <Typography
              variant="caption"
              sx={{
                backgroundColor: theme.palette.specialColors.grey[100],
                borderRadius: "4px",
                padding: "3px 6px",
                cursor: "pointer",
                minWidth: 170,
              }}
            >
              {moment(document.expiry).format("DD MMM yyyy")}
            </Typography>
          ) : (
            <Typography
              variant="caption"
              sx={{
                color: theme.palette.primary.main,
                cursor: "pointer",
                "&:hover": {
                  color: theme.palette.primary.main,
                },
              }}
              data-testid="add-expiry-date"
              noWrap
            >
              {t("addExpiry")}
            </Typography>
          )}
          <DesktopDatePicker
            onChange={(e: moment.Moment | null) => {
              setDateToChange(e?.utc().format() || "");
              const newDocumentExpiry: string = e?.utc().format() || "";
              const newDocumentExpiryMD = getMonthAndDay(newDocumentExpiry);
              const dateToChangeMD = getMonthAndDay(dateToChange || "");

              if (
                JSON.stringify(newDocumentExpiryMD) !== JSON.stringify(dateToChangeMD) ||
                newChosenYear
              ) {
                const updatedExpiry = newChosenYear
                  ? e?.utc().year(newChosenYear).format()
                  : newDocumentExpiry;
                updateDocumentItem({ expiry: updatedExpiry }, document.id);
                setAnchorDateEl(null);
                setNewChosenYear(null);
                if (refreshDocumentCounts) {
                  refreshDocumentCounts();
                }
              } else {
                setNewChosenYear(e?.utc().year() || null);
              }
            }}
            closeOnSelect={false}
            open={Boolean(anchorDateEl)}
            renderInput={props => <TextField variant="standard" {...props} />}
            value={dateToChange || new Date().toUTCString()}
            onClose={() => {
              setAnchorDateEl(null);
              setNewChosenYear(null);
            }}
            InputProps={{
              style: { position: "absolute", width: 0, overflow: "hidden" },
            }}
          />
        </div>
      </TableCell>

      <TableCell sx={{ width: 100 }} className={classes.tableCell}>
        <DocumentStatusSelect
          value={document.status}
          onChange={async status => {
            await updateDocumentItem({ status }, document.id);
            if (refreshDocumentCounts) {
              refreshDocumentCounts();
            }
          }}
          selectable={true}
        />
      </TableCell>
      <TableCell
        width="13%"
        align="center"
        padding="none"
        onClick={e => {
          e.preventDefault();
        }}
        className={classes.tableCell}
        sx={{ align: "center", maxWidth: 130 }}
      >
        <DocumentItemPermissions
          style={{ marginTop: 0, paddingLeft: 0 }}
          documentId={document.id}
          hidePermission={false}
          permission={document.permission || null}
          readOnly={false}
        />
      </TableCell>
      <TableCell
        width="13%"
        padding="none"
        onClick={e => {
          e.preventDefault();
        }}
        className={classes.tableCell}
        sx={{ width: "10%" }}
      >
        <Typography
          variant="caption"
          sx={{
            backgroundColor: theme.palette.specialColors.grey[100],
            borderRadius: "4px",
            padding: "3px 6px",
            cursor: "pointer",
            minWidth: 170,
          }}
        >
          {moment(document.createdAt).format("DD MMM yyyy")}
        </Typography>
      </TableCell>
      <TableCell className={classes.tableCell} sx={{ width: "4%", marginRight: 10 }}>
        <DocumentPopupMenu
          document={document}
          updateDocumentItem={updateDocumentItem}
          setWarningModalAction={setWarningModalAction}
          setOpenConfirmDeletion={setOpenConfirmDeletion}
          setOpenRenameModal={setOpenRenameModal}
        />
      </TableCell>
      {selectedTab === DocumentTabs.EXPIRING && (
        <TableCell
          sx={{
            padding: "0px",
            paddingLeft: "6px",
            paddingRight: "14px",
            "& .css-1rvd36z-MuiFormControlLabel-root": {
              marginLeft: "0px",
              marginRight: "0px",
            },
          }}
          width={50}
        >
          <Checkbox
            className={classes.checkBox}
            onClick={e => {
              e.stopPropagation();
            }}
            checked={done}
            onChange={changeDoneCheckBox}
            name="DONE"
            color="primary"
            disabled={loadingSetDone}
            sx={{ margin: "0px" }}
          />
        </TableCell>
      )}
    </TableRow>
  );
};

const useStyles = makeStyles<Theme>(theme => ({
  fileCell: { flexWrap: "nowrap" },
  link: { textDecoration: "none", color: "unset" },
  fileIcon: { width: 24, minWidth: 24, marginRight: 20, marginLeft: theme.spacing(1) },
  checkBox: { paddingTop: 0, paddingBottom: 0 },
  tableCell: { paddingRight: 5, paddingLeft: 5 },
}));
