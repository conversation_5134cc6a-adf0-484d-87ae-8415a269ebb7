import { Autocomplete, Grid, TextField, Chip } from "@mui/material";
import { DOCUMENT_STATUS, useQueryString } from "@rentancy/common";
import { DocumentQueryType } from "../../DocumentsProvider";
import { FC } from "react";
import { useTranslation } from "react-i18next";

export const DocumentStatusFilter: FC = () => {
  const { query, setQuery } = useQueryString<DocumentQueryType>();
  const { t } = useTranslation();

  return (
    <Grid item xs={2}>
      <Autocomplete
        id={"autocomplete-status"}
        multiple
        disableCloseOnSelect
        options={DOCUMENT_STATUS}
        disabled={false}
        getOptionLabel={option => option.text}
        value={DOCUMENT_STATUS.filter(item => (query.statuses ?? "").includes(item.value))}
        renderInput={params => (
          <TextField
            {...params}
            fullWidth
            variant="outlined"
            size="small"
            placeholder={t("status")}
          />
        )}
        sx={{
          "& .MuiAutocomplete-tag": {
            margin: "0px",
          },
          "& .MuiChip-deleteIcon": {
            display: "none",
          },
        }}
        onChange={(_, value) => {
          setQuery({
            ...query,
            statuses: value.map(item => item.value).join(","),
          });
        }}
        renderTags={(value, getTagProps) =>
          value.length > 0 ? (
            <Chip
              color="info"
              sx={{ height: "25px" }}
              label={`${value.length} ${t("selected")}`}
              {...getTagProps({ index: 0 })}
            />
          ) : null
        }
      />
    </Grid>
  );
};
