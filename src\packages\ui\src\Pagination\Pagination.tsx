import usePagination, { UsePaginationProps } from "@mui/material/usePagination";
import { ArrowBackIosRounded, ArrowForwardIosRounded } from "@mui/icons-material";
import { alpha, Grid, MenuItem, TextField, Theme } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useCallback, useState } from "react";
import { useLocation, useNavigate } from "react-router";
import { useSnackbar } from "notistack";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import { useDeviceScreen } from "@rentancy/common";

export interface PaginationProps extends UsePaginationProps {
  onLimitChange?: (limit: number) => void;
  paginationLimit?: string;
  pageSizeArr?: string[];
  hideLimitSelector?: boolean;
  hideGoTo?: boolean;
  queryString?: boolean;
  className?: string;
  useDefaultLimitBehavior?: boolean;
  shrink?: boolean;
}
const defaultSizeArr = ["10", "20", "30", "50", "70", "100"];
export const Pagination = ({
  onLimitChange,
  hideLimitSelector,
  paginationLimit = "10",
  queryString = true,
  pageSizeArr = defaultSizeArr,
  className,
  hideGoTo = false,
  useDefaultLimitBehavior = true,
  shrink = false,
  ...props
}: PaginationProps) => {
  const [goto, setGoto] = useState<number>();

  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslation();
  const classes = useStyles({ shrink });
  const navigate = useNavigate();
  const location = useLocation();
  const { isDesktop } = useDeviceScreen();

  const query = new URLSearchParams(location.search); // formerly used "const location = useLocation();"

  const getQueryObj = useCallback(
    (transQuery: URLSearchParams) =>
      [...transQuery.entries()].reduce(
        (obj, [key, value]) => {
          obj[key] = value;
          return obj;
        },
        {} as Record<string, string>,
      ),
    [],
  );

  const onChange = (event: React.ChangeEvent<unknown>, page: number) => {
    // maybe we should keep the trigger of `onChange`
    props.onChange && props.onChange(event, page);
    // Clear the original query may cause some side effects, so each navigation keeps original query object.
    if (queryString) {
      navigate(
        {
          search:
            "?" +
            new URLSearchParams({
              ...getQueryObj(query),
              page: page.toString(),
              limit: query.get("limit") ?? paginationLimit,
            }).toString(),
        },
        { replace: true },
      );
    }
  };
  const { items } = usePagination({
    ...props,
    hidePrevButton: true,
    hideNextButton: true,
    count: props.count || 1,
    page: props.page || Number(query.get("page")) || 1,
    onChange,
  });
  return (
    <Grid
      component="nav"
      container
      justifyContent="center"
      className={clsx(classes.root, className)}
      wrap="nowrap"
      columnSpacing={1}
      alignItems="center"
    >
      <Grid item container justifyContent="center" xs={shrink ? "auto" : false}>
        <ul className={classes.ul}>
          {items.map(({ page, type, selected, ...item }, index) => {
            let children = null;

            if (type === "start-ellipsis" || type === "end-ellipsis") {
              children = (
                <button type="button" style={{ border: "none", cursor: "default" }} {...item}>
                  …
                </button>
              );
            } else if (type === "page") {
              children = (
                <button
                  type="button"
                  className={selected ? classes.selectedButton : classes.unselectedButton}
                  {...item}
                >
                  {page}
                </button>
              );
            } else if (type === "previous") {
              children = (
                <button type="button" style={{ marginRight: 10, borderRadius: 4 }} {...item}>
                  <ArrowBackIosRounded style={{ width: 12 }} />
                </button>
              );
            } else if (type === "next") {
              children = (
                <button type="button" style={{ marginLeft: 10, borderRadius: 4 }} {...item}>
                  <ArrowForwardIosRounded style={{ width: 12 }} />
                </button>
              );
            } else {
              children = (
                <button type="button" {...item}>
                  {type}
                </button>
              );
            }
            return <li key={index}>{children}</li>;
          })}
        </ul>
        {!hideGoTo && isDesktop && (
          <form
            onSubmit={e => {
              try {
                e.preventDefault();
                if (!goto || goto <= 0) {
                  throw new Error(t("pleaseEnterValidNumber"));
                }
                if (props.count && goto > props.count) {
                  throw new Error(
                    t("thePageNumberMustNotBeGreaterThanPropsCount", { count: props.count }),
                  );
                }
                onChange({ target: null } as unknown as React.ChangeEvent<unknown>, goto);
                setGoto(undefined);
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
              } catch (error: any) {
                enqueueSnackbar(error.message, { variant: "error" });
              }
            }}
          >
            <TextField
              variant="outlined"
              type="number"
              size="small"
              placeholder={t("general.pagination.goTo")}
              value={goto}
              className={classes.goto}
              disabled={props.disabled}
              onChange={e => setGoto(e.target.value ? Number(e.target.value) : undefined)}
            />
            <button type="submit" style={{ display: "none" }} />
          </form>
        )}
      </Grid>
      {!hideLimitSelector && (
        <Grid item display="flex" alignItems="center">
          <TextField
            variant="outlined"
            select
            size="small"
            name="listingType"
            disabled={props.disabled}
            className={classes.input}
            value={
              useDefaultLimitBehavior ? query.get("limit") || paginationLimit : paginationLimit
            }
          >
            {pageSizeArr.map(item => (
              <MenuItem
                key={item}
                value={item}
                onClick={() => {
                  queryString &&
                    navigate({
                      search:
                        "?" +
                        new URLSearchParams({
                          ...getQueryObj(query),
                          page: "1",
                          limit: item,
                        }).toString(),
                    });
                  onLimitChange && onLimitChange(+item);
                }}
              >
                <>
                  {t("general.pagination.show")}: {item}
                </>
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      )}
    </Grid>
  );
};

type StyleProps = {
  shrink?: boolean;
};

const useStyles = makeStyles<Theme, StyleProps>(theme => ({
  root: {
    // width: ({ shrink }) => (shrink ? "fit-content" : "100%"),
    // marginTop: theme.spacing(1.25),
    display: "flex",
    alignItems: "center",
    border: "1px solid #E1E2E6",
    borderRadius: 6,
    // padding: 6,
  },
  ul: {
    listStyle: "none",
    padding: 0,
    margin: 0,
    display: "flex",
    "& button": {
      width: theme.spacing(4),
      height: theme.spacing(4),
      cursor: "pointer",
      background: theme.palette.common.white,
      border: `1px solid ${theme.palette.specialColors.themeBorder}`,
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      "&:focus": {
        outline: "unset",
      },
    },
  },
  goto: {
    width: 100,
    marginLeft: 10,
    backgroundColor: theme.palette.background.paper,
    "& input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button":
      {
        "-webkit-appearance": "none",
        margin: 0,
      },
    "& .MuiInputBase-root": {
      height: theme.spacing(4),
    },
  },
  input: {
    backgroundColor: theme.palette.background.paper,
    "& > div > div": {
      fontSize: 14,
    },
    "& .MuiInputBase-root": {
      height: theme.spacing(4),
    },
  },
  selectedButton: {
    color: theme.palette.primary.main,
    fontWeight: 700,
    borderRadius: 4,
    border: "unset !important",
  },
  unselectedButton: {
    color: theme.palette.text.secondary2,
    borderRadius: 4,
    border: "unset !important",
  },
}));
