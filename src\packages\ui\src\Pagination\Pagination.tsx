import usePagination, { UsePaginationProps } from "@mui/material/usePagination";
import { ArrowBackIosRounded, ArrowForwardIosRounded } from "@mui/icons-material";
import { alpha, Grid, MenuItem, TextField, Theme } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useCallback, useState } from "react";
import { useLocation, useNavigate } from "react-router";
import { useSnackbar } from "notistack";
import { useTranslation } from "react-i18next";
import clsx from "clsx";
import { useDeviceScreen } from "@rentancy/common";

export interface PaginationProps extends UsePaginationProps {
  onLimitChange?: (limit: number) => void;
  paginationLimit?: string;
  pageSizeArr?: string[];
  hideLimitSelector?: boolean;
  hideGoTo?: boolean;
  queryString?: boolean;
  className?: string;
  useDefaultLimitBehavior?: boolean;
  shrink?: boolean;
}
const defaultSizeArr = ["10", "20", "30", "50", "70", "100"];
export const Pagination = ({
  onLimitChange,
  hideLimitSelector,
  paginationLimit = "10",
  queryString = true,
  pageSizeArr = defaultSizeArr,
  className,
  hideGoTo = false,
  useDefaultLimitBehavior = true,
  shrink = false,
  ...props
}: PaginationProps) => {
  const [goto, setGoto] = useState<number>();

  const { enqueueSnackbar } = useSnackbar();
  const { t } = useTranslation();
  const classes = useStyles({ shrink });
  const navigate = useNavigate();
  const location = useLocation();
  const { isDesktop } = useDeviceScreen();

  const query = new URLSearchParams(location.search); // formerly used "const location = useLocation();"

  const getQueryObj = useCallback(
    (transQuery: URLSearchParams) =>
      [...transQuery.entries()].reduce(
        (obj, [key, value]) => {
          obj[key] = value;
          return obj;
        },
        {} as Record<string, string>,
      ),
    [],
  );

  const onChange = (event: React.ChangeEvent<unknown>, page: number) => {
    // maybe we should keep the trigger of `onChange`
    props.onChange && props.onChange(event, page);
    // Clear the original query may cause some side effects, so each navigation keeps original query object.
    if (queryString) {
      navigate(
        {
          search:
            "?" +
            new URLSearchParams({
              ...getQueryObj(query),
              page: page.toString(),
              limit: query.get("limit") ?? paginationLimit,
            }).toString(),
        },
        { replace: true },
      );
    }
  };
  const { items } = usePagination({
    ...props,
    hidePrevButton: true,
    hideNextButton: true,
    count: props.count || 1,
    page: props.page || Number(query.get("page")) || 1,
    onChange,
  });
  return (
    <Grid
      component="nav"
      container
      justifyContent="center"
      className={clsx(classes.root, className)}
      wrap="nowrap"
      columnSpacing={1}
      alignItems="center"
    >
      <Grid item container justifyContent="center" xs={shrink ? "auto" : false}>
        <ul className={classes.ul}>
          {items.map(({ page, type, selected, ...item }, index) => {
            let children = null;

            if (type === "start-ellipsis" || type === "end-ellipsis") {
              children = (
                <button type="button" className={classes.ellipsis} {...item}>
                  …
                </button>
              );
            } else if (type === "page") {
              children = (
                <button
                  type="button"
                  className={selected ? classes.selectedButton : classes.unselectedButton}
                  {...item}
                >
                  {page}
                </button>
              );
            } else if (type === "previous") {
              children = (
                <button type="button" className={classes.arrowButton} {...item}>
                  <ArrowBackIosRounded style={{ width: 14, height: 14 }} />
                </button>
              );
            } else if (type === "next") {
              children = (
                <button type="button" className={classes.arrowButton} {...item}>
                  <ArrowForwardIosRounded style={{ width: 14, height: 14 }} />
                </button>
              );
            } else {
              children = (
                <button type="button" className={classes.unselectedButton} {...item}>
                  {type}
                </button>
              );
            }
            return <li key={index}>{children}</li>;
          })}
        </ul>
        {!hideGoTo && isDesktop && (
          <form
            onSubmit={e => {
              try {
                e.preventDefault();
                if (!goto || goto <= 0) {
                  throw new Error(t("pleaseEnterValidNumber"));
                }
                if (props.count && goto > props.count) {
                  throw new Error(
                    t("thePageNumberMustNotBeGreaterThanPropsCount", { count: props.count }),
                  );
                }
                onChange({ target: null } as unknown as React.ChangeEvent<unknown>, goto);
                setGoto(undefined);
                // eslint-disable-next-line @typescript-eslint/no-explicit-any
              } catch (error: any) {
                enqueueSnackbar(error.message, { variant: "error" });
              }
            }}
          >
            <TextField
              variant="outlined"
              type="number"
              size="small"
              placeholder={t("general.pagination.goTo")}
              value={goto}
              className={classes.goto}
              disabled={props.disabled}
              onChange={e => setGoto(e.target.value ? Number(e.target.value) : undefined)}
            />
            <button type="submit" style={{ display: "none" }} />
          </form>
        )}
      </Grid>
      {!hideLimitSelector && (
        <Grid item display="flex" alignItems="center">
          <TextField
            variant="outlined"
            select
            size="small"
            name="listingType"
            disabled={props.disabled}
            className={classes.input}
            value={
              useDefaultLimitBehavior ? query.get("limit") || paginationLimit : paginationLimit
            }
          >
            {pageSizeArr.map(item => (
              <MenuItem
                key={item}
                value={item}
                onClick={() => {
                  queryString &&
                    navigate({
                      search:
                        "?" +
                        new URLSearchParams({
                          ...getQueryObj(query),
                          page: "1",
                          limit: item,
                        }).toString(),
                    });
                  onLimitChange && onLimitChange(+item);
                }}
              >
                <>
                  {t("general.pagination.show")}: {item}
                </>
              </MenuItem>
            ))}
          </TextField>
        </Grid>
      )}
    </Grid>
  );
};

type StyleProps = {
  shrink?: boolean;
};

const useStyles = makeStyles<Theme, StyleProps>(theme => ({
  root: {
    display: "flex",
    alignItems: "center",
    border: "1px solid #E1E2E6",
    borderRadius: 6,
    padding: theme.spacing(0.5),
    backgroundColor: theme.palette.common.white,
  },
  ul: {
    listStyle: "none",
    padding: 0,
    margin: 0,
    display: "flex",
    gap: theme.spacing(0.5),
    "& button": {
      width: theme.spacing(4),
      height: theme.spacing(4),
      cursor: "pointer",
      background: "transparent",
      border: "none",
      borderRadius: "50%",
      display: "flex",
      justifyContent: "center",
      alignItems: "center",
      fontSize: "14px",
      fontWeight: 500,
      transition: "all 0.2s ease-in-out",
      "&:hover": {
        backgroundColor: theme.palette.specialColors.grey[100],
      },
      "&:focus": {
        outline: "unset",
      },
      "&:disabled": {
        cursor: "not-allowed",
        opacity: 0.5,
      },
    },
  },
  goto: {
    width: 100,
    marginLeft: theme.spacing(1.5),
    backgroundColor: theme.palette.background.paper,
    "& input[type=number]::-webkit-inner-spin-button, input[type=number]::-webkit-outer-spin-button":
      {
        "-webkit-appearance": "none",
        margin: 0,
      },
    "& .MuiInputBase-root": {
      height: theme.spacing(4),
      borderRadius: theme.spacing(0.5),
    },
  },
  input: {
    backgroundColor: theme.palette.background.paper,
    marginLeft: theme.spacing(1.5),
    "& > div > div": {
      fontSize: 14,
    },
    "& .MuiInputBase-root": {
      height: theme.spacing(4),
      borderRadius: theme.spacing(0.5),
    },
  },
  selectedButton: {
    backgroundColor: `${theme.palette.primary.main} !important`,
    color: `${theme.palette.common.white} !important`,
    fontWeight: 600,
    "&:hover": {
      backgroundColor: `${alpha(theme.palette.primary.main, 0.8)} !important`,
    },
  },
  unselectedButton: {
    color: theme.palette.text.secondary2,
    backgroundColor: "transparent",
    "&:hover": {
      backgroundColor: theme.palette.specialColors.grey[100],
    },
  },
  arrowButton: {
    color: theme.palette.text.secondary2,
    backgroundColor: "transparent",
    "&:hover": {
      backgroundColor: theme.palette.specialColors.grey[100],
    },
    "&:disabled": {
      color: theme.palette.specialColors.grey[400],
    },
  },
  ellipsis: {
    color: theme.palette.text.secondary2,
    backgroundColor: "transparent",
    cursor: "default",
    "&:hover": {
      backgroundColor: "transparent !important",
    },
  },
}));
