import {
  getAllDocuments,
  newGetAllDocuments,
  newGetPropertyByName,
  useApi,
  useUser,
  Document,
  Role,
  useQueryString,
  listContactsForSelect,
  ContactDetailsForSelect,
  DOCUMENT_TYPES_UK,
  DOCUMENT_TYPES_US,
  DOCUMENT_TYPES_GROUPED_UK,
  DOCUMENT_TYPES_GROUPED_US,
  PropertyDetails,
  DirectionType,
  DocumentTypeGroup,
  getFilteredDocumentCount,
} from "@rentancy/common";
import { FC, ReactNode, useMemo, createContext, useCallback, useEffect, useState } from "react";
import { DocumentTabs } from "./Components/DocumentsHeader";
import { SupportedLanguages } from "@rentancy/common";
import { debounce } from "underscore";

interface DocumentContextInterface {
  Status: FC;
  documentTypes: typeof DOCUMENT_TYPES_UK | typeof DOCUMENT_TYPES_US;
  documentTypesGrouped: DocumentTypeGroup[];
  supportedLanguages: SupportedLanguages[];
  loading: boolean;
  dayFilter: number;
  searchPhrase: string;
  propertyFilter: Array<String>;
  documentCategory: string;
  selectedTab: DocumentTabs;
  toShowDocuments?: Document[];
  total: number;
  landlordList?: ContactDetailsForSelect[];
  propertiesList: Array<Partial<PropertyDetails>>;
  documentsCounts: { [key in DocumentTabs]: number };
  setDayFilter: (day: number) => void;
  setDocumentCategory: (category: string) => void;
  setSearchPhrase: (phrase: string) => void;
  setPropertyFilter: (property: Array<String>) => void;
  setSelectedTab: (tab: DocumentTabs) => void;
  getData: () => void;
  refreshDocumentCounts?: () => void;
  setQuery?: (query: Partial<DocumentQueryType>) => void;
}

export type SortDocumentType = "createdAt";

export interface DocumentQueryType {
  page?: string;
  limit?: string;
  types?: string;
  statuses?: string;
  createdAtTo?: string;
  createdAtFrom?: string;
  orderBy?: SortDocumentType;
  orderDirection?: DirectionType;
}

export const DocumentContext = createContext<DocumentContextInterface>({
  Status: () => null,
  documentTypes: [],
  documentTypesGrouped: [],
  supportedLanguages: [
    {
      code: "",
      name: "",
    },
  ],
  loading: false,
  dayFilter: 30,
  searchPhrase: "",
  documentCategory: "",
  propertyFilter: [],
  selectedTab: DocumentTabs.ALL,
  toShowDocuments: [],
  total: 0,
  propertiesList: [],
  documentsCounts: {
    [DocumentTabs.ALL]: 0,
    [DocumentTabs.ARCHIVE]: 0,
    [DocumentTabs.EXPIRING]: 0,
  },
  setDayFilter: (_day: number) => {},
  setDocumentCategory: (_category: string) => {},
  setSearchPhrase: (_phrase: string) => {},
  setPropertyFilter: (_property: Array<String>) => {},
  setSelectedTab: (_tab: DocumentTabs) => {},
  getData: () => {},
  refreshDocumentCounts: () => {},
  setQuery: (_query: Partial<DocumentQueryType>) => {},
});

export const EMPTY_DOCUMENT_DATA: Array<Document> = [];

export const DocumentsProvider: FC<{
  children: ReactNode;
  location: string;
  apiUrl: string;
  supportedLanguages: SupportedLanguages[];
  Status: FC;
}> = ({ children, location, apiUrl, supportedLanguages, Status }) => {
  const { Provider } = DocumentContext;

  const [dayFilter, setDayFilter] = useState<number>(30);
  const [documentCategory, setDocumentCategory] = useState<string>("");
  const [searchPhrase, setSearchPhrase] = useState<string>("");
  const [propertyFilter, setPropertyFilter] = useState<Array<String>>([]);
  const [selectedTab, setSelectedTab] = useState<DocumentTabs>(DocumentTabs.ALL);
  const [allDocuments, setAllDocuments] = useState<Document[]>(EMPTY_DOCUMENT_DATA);
  const [toShowDocuments, setToShowDocuments] = useState<Document[]>(EMPTY_DOCUMENT_DATA);

  const [propertiesList, setPropertiesList] = useState<Partial<PropertyDetails>[]>([]);

  const { user } = useUser();
  const organisationId = user.organisationId;

  const documentTypes = location === "UK" ? DOCUMENT_TYPES_UK : DOCUMENT_TYPES_US;
  const documentTypesGrouped =
    location === "UK" ? DOCUMENT_TYPES_GROUPED_UK : DOCUMENT_TYPES_GROUPED_US;

  const { setQuery, query } = useQueryString<DocumentQueryType>();

  const { apiCall: landlordApi, data: landlordList } = useApi(listContactsForSelect);
  const { apiCall: getDocumentsApi, loading } = useApi(getAllDocuments);

  const getData = useCallback(async () => {
    const resDocuments = await getDocumentsApi({
      organisationId,
      documentType: query.types ?? null,
      daysRange: DocumentTabs.EXPIRING === selectedTab ? dayFilter : null,
      statuses: query.statuses ?? null,
      prefix: apiUrl,
      createdAtFrom: query.createdAtFrom ?? null,
      createdAtTo: query.createdAtTo ?? null,
    });
    if (!resDocuments.data) {
      return;
    }
    setAllDocuments(resDocuments.data.sortedData);
  }, [
    apiUrl,
    organisationId,
    selectedTab,
    dayFilter,
    getDocumentsApi,
    query.types,
    query.statuses,
    query.createdAtFrom,
    query.createdAtTo,
  ]);

  useEffect(() => {
    let searchedDocuments: (Document | undefined)[] = [];

    switch (selectedTab) {
      case DocumentTabs.EXPIRING: {
        const expiringDocuments = allDocuments?.filter(document => document?.status === "EXPIRING");
        searchedDocuments = expiringDocuments;
        break;
      }
      case DocumentTabs.ALL: {
        searchedDocuments = allDocuments;
        break;
      }
      case DocumentTabs.ARCHIVE: {
        const archivedDocuments = allDocuments?.filter(document => document?.archived === true);
        searchedDocuments = archivedDocuments;
        break;
      }
    }

    if (!searchedDocuments) {
      return;
    }

    const properties: Partial<PropertyDetails>[] = searchedDocuments
      ?.map(document => ({
        name: document?.property?.addressLine1,
        id: document?.property?.id,
      }))
      ?.filter(property => property.name !== undefined);

    const propertiesWithNoDuplicates = (properties: Partial<PropertyDetails>[]) => {
      const seenIds = new Set();
      return properties.filter(property => {
        if (seenIds.has(property.id)) {
          return false;
        } else {
          seenIds.add(property.id);
          return true;
        }
      });
    };
    setPropertiesList(propertiesWithNoDuplicates(properties));

    searchedDocuments = searchedDocuments.filter(document =>
      document?.name?.toLowerCase().includes(searchPhrase.toLowerCase()),
    );

    if (propertyFilter.length > 0) {
      searchedDocuments = searchedDocuments.filter(document =>
        propertyFilter.includes(document?.property?.id || ""),
      );
    }
    if (query.types) {
      const filterArr = query.types.split(",");
      searchedDocuments = searchedDocuments.filter(document =>
        filterArr.includes(document?.type || ""),
      );
    }
    setToShowDocuments(searchedDocuments as Document[]);

    setQuery({ page: 1 });
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [
    searchPhrase,
    allDocuments,
    setToShowDocuments,
    selectedTab,
    documentCategory,
    propertyFilter,
    query.types,
  ]);

  useEffect(() => {
    getData();
  }, [getData, selectedTab]);

  const refreshDocumentCounts = useCallback(() => {
    // For the old provider, we don't have getDocumentCountApi, so we'll just refresh data
    getData();
  }, [getData]);

  useEffect(() => {
    landlordApi({
      types: [Role.LANDLORD],
    });
  }, [landlordApi]);
  return (
    <Provider
      value={{
        Status,
        documentTypes,
        documentTypesGrouped,
        supportedLanguages,
        loading,
        dayFilter,
        selectedTab,
        landlordList: landlordList?.users,
        searchPhrase,
        toShowDocuments,
        propertiesList,
        getData,
        documentCategory,
        propertyFilter,
        setDayFilter,
        setDocumentCategory,
        setPropertyFilter,
        setSearchPhrase,
        setSelectedTab,
        refreshDocumentCounts,
        setQuery,
      }}
    >
      {children}
    </Provider>
  );
};

export const NewDocumentsProvider: FC<{
  children: ReactNode;
  location: string;
  apiUrl: string;
  supportedLanguages: SupportedLanguages[];
  Status: FC;
}> = ({ children, location, apiUrl, supportedLanguages, Status }) => {
  const { Provider } = DocumentContext;

  const [dayFilter, setDayFilter] = useState<number>(30);
  const [documentCategory, setDocumentCategory] = useState<string>("");
  const [searchPhrase, setSearchPhrase] = useState<string>("");
  const [searchProperties, setSearchProperties] = useState<string>("");
  const [propertyFilter, setPropertyFilter] = useState<Array<String>>([]);
  const [selectedTab, setSelectedTab] = useState<DocumentTabs>(DocumentTabs.ALL);
  // const [allDocuments, setAllDocuments] = useState<Document[]>(EMPTY_DOCUMENT_DATA);
  const [total, setTotals] = useState<number>(0);
  const [toShowDocuments, setToShowDocuments] = useState<Document[]>(EMPTY_DOCUMENT_DATA);

  const [propertiesList, setPropertiesList] = useState<Partial<PropertyDetails>[]>([]);

  const { user } = useUser();
  const organisationId = user.organisationId;

  const documentTypes = location === "UK" ? DOCUMENT_TYPES_UK : DOCUMENT_TYPES_US;
  const documentTypesGrouped =
    location === "UK" ? DOCUMENT_TYPES_GROUPED_UK : DOCUMENT_TYPES_GROUPED_US;

  const { query, setQuery } = useQueryString<DocumentQueryType>();

  const { apiCall: landlordApi, data: landlordList } = useApi(listContactsForSelect);
  const { apiCall: getDocumentsApi, loading } = useApi(newGetAllDocuments);
  const { apiCall: getPropertyApi } = useApi(newGetPropertyByName);
  const { apiCall: getDocumentCountApi } = useApi(getFilteredDocumentCount);
  const [documentsCounts, setDocumentsCounts] = useState<{
    [key in DocumentTabs]: number;
  }>({
    [DocumentTabs.ALL]: 0,
    [DocumentTabs.ARCHIVE]: 0,
    [DocumentTabs.EXPIRING]: 0,
  });

  const getData = useCallback(
    async (searchPhrase = "") => {
      const resDocuments = await getDocumentsApi({
        organisationId,
        documentType: query.types ?? null,
        daysRange: DocumentTabs.EXPIRING === selectedTab ? dayFilter : null,
        statuses: query.statuses ?? null,
        propertyIds: propertyFilter,
        prefix: apiUrl,
        createdAtFrom: query.createdAtFrom ?? null,
        createdAtTo: query.createdAtTo ?? null,
        page: query?.page || 1,
        limit: query?.limit || 30,
        searchPhrase: searchPhrase,
        selectedTab: selectedTab,
        orderDirection: query.orderDirection ?? null,
      });

      if (!resDocuments.data) {
        return;
      }
      setToShowDocuments(resDocuments?.data?.data?.data);
      setTotals(resDocuments?.data?.data?.total ?? 0);
    },
    [
      apiUrl,
      organisationId,
      selectedTab,
      dayFilter,
      getDocumentsApi,
      propertyFilter,
      query.types,
      query.statuses,
      query.createdAtFrom,
      query.createdAtTo,
      query.page,
      query.limit,
      query.orderDirection,
    ],
  );

  const handleChangeDebounced = useMemo(() => {
    return debounce(getData, 500);
  }, [getData]);

  useEffect(() => {
    handleChangeDebounced(searchPhrase);
  }, [handleChangeDebounced, searchPhrase]);

  useEffect(() => {
    getData();
  }, [getData, selectedTab]);

  useEffect(() => {
    if (searchProperties) {
      getPropertyApi(searchProperties).then(result => {
        setPropertiesList(result?.data?.data);
      });
    }
  }, [getPropertyApi, searchProperties]);

  useEffect(() => {
    landlordApi({
      types: [Role.LANDLORD],
    });
  }, [landlordApi]);

  const refreshDocumentCounts = useCallback(() => {
    getDocumentCountApi().then(result => {
      setDocumentsCounts({
        [DocumentTabs.ALL]: result?.data?.data?.all ?? 0,
        [DocumentTabs.EXPIRING]: result?.data?.data?.expiring ?? 0,
        [DocumentTabs.ARCHIVE]: result?.data?.data?.archived ?? 0,
      });
    });
  }, [getDocumentCountApi]);

  useEffect(() => {
    refreshDocumentCounts();
  }, [refreshDocumentCounts]);

  return (
    <Provider
      value={{
        Status,
        documentTypes,
        documentTypesGrouped,
        supportedLanguages,
        loading,
        dayFilter,
        selectedTab,
        landlordList: landlordList?.users,
        searchPhrase,
        setSearchProperties,
        toShowDocuments,
        total,
        propertiesList,
        getData,
        documentCategory,
        propertyFilter,
        setDayFilter,
        setDocumentCategory,
        setPropertyFilter,
        setSearchPhrase,
        setSelectedTab,
        documentsCounts,
        refreshDocumentCounts,
        setQuery,
      }}
    >
      {children}
    </Provider>
  );
};
