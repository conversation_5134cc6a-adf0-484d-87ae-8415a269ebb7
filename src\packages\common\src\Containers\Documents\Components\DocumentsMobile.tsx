import { CSSProperties, FC, useContext, useEffect, useState } from "react";
import { makeStyles } from "@mui/styles";
import { Grid, Skeleton, useTheme, Fab, Box, Theme } from "@mui/material";
import { Add } from "@mui/icons-material";
import {
  Document,
  useApi,
  useQueryString,
  updateDocument,
  DEFAULT_PAGINATION_LIMIT,
  type UpdateDocument,
  type OpenConfirmDeletion,
  type WarningModalAction,
} from "@rentancy/common";
import { DocumentContext } from "../DocumentsProvider";
import { Pagination } from "@rentancy/ui";
import { useNavigate } from "react-router-dom";
import { EditMobileDocument } from "./EditMobileDocument";
import { PopupModals } from "../../../Components/Documents/PopupModals";
import { DocumentCardMobile } from "./DocumentCardMobile";

export const DocumentsMobile: FC = () => {
  const classes = useStyles();
  const theme = useTheme();
  const [pageCount, setPageCount] = useState(0);
  const [dataToShow, setDataToShow] = useState<Document[]>([]);
  const navigate = useNavigate();

  const { query } = useQueryString<{
    page?: string;
    limit?: string;
  }>();

  const [documentForEditing, setDocumentForEditing] = useState<null | Document>(null);
  const [openConfirmDeletion, setOpenConfirmDeletion] = useState<OpenConfirmDeletion | null>(null);

  const { apiCall: updateDocumentApi } = useApi(updateDocument);

  const [warningModalAction, setWarningModalAction] = useState<WarningModalAction | null>(null);

  const updateDocumentItem = async (props: Partial<UpdateDocument>, id: string) => {
    await updateDocumentApi({ ...props, id }, { followingFunctions: refreshData });
    if (refreshDocumentCounts) {
      refreshDocumentCounts();
    }
    setWarningModalAction(null);
  };

  const {
    toShowDocuments = [],
    loading,
    getData: refreshData,
    refreshDocumentCounts,
  } = useContext(DocumentContext);

  useEffect(() => {
    const pageSize = query.limit ? parseInt(query.limit) : parseInt(DEFAULT_PAGINATION_LIMIT);
    const pageNum = query.page ? parseInt(query.page) : 1;
    const tmpDataToShow =
      toShowDocuments?.slice((pageNum - 1) * pageSize, pageSize * pageNum) || [];
    setDataToShow(tmpDataToShow);
    setPageCount(Math.ceil(toShowDocuments.length / pageSize));
  }, [query.page, query.limit, setPageCount, toShowDocuments]);

  return (
    <Grid
      container
      sx={{
        display: "flex",
        flexDirection: "column",
        height: "100%",
        position: "relative",
        overflow: "scroll",
        paddingBottom: "80px",
      }}
    >
      <Fab
        color="primary"
        aria-label="add"
        sx={{
          position: "fixed",
          right: 23,
          bottom: 26,
          backgroundColor: theme.palette.secondary.main,
          color: "white",
        }}
        onClick={() => {
          navigate("/upload", { state: { backUrl: window.location.pathname } });
        }}
      >
        <Add />
      </Fab>
      {documentForEditing && (
        <EditMobileDocument
          isOpen={Boolean(documentForEditing)}
          onClose={() => setDocumentForEditing(null)}
          document={documentForEditing}
        />
      )}
      <PopupModals
        warningModalAction={warningModalAction}
        setWarningModalAction={setWarningModalAction}
        updateDocumentItem={updateDocumentItem}
        openConfirmDeletion={openConfirmDeletion}
        setOpenConfirmDeletion={setOpenConfirmDeletion}
      />
      {loading && (
        <Box sx={{ flexGrow: 1 }}>
          <LoadingSkeleton />
          <LoadingSkeleton />
          <LoadingSkeleton style={{ opacity: 0.8 }} />
          <LoadingSkeleton style={{ opacity: 0.5 }} />
          <LoadingSkeleton style={{ opacity: 0.2 }} />
        </Box>
      )}
      <Box
        sx={{
          display: "flex",
          flexGrow: 1,
          flexDirection: "column",
        }}
      >
        {!loading && dataToShow && (
          <Grid
            container
            sx={{
              display: "flex",
              flexDirection: "column",
              gap: "6px",
            }}
          >
            {dataToShow?.map((item: Document) => {
              const documentType = item.mimeType?.split("/")[1] || "";
              return (
                <DocumentCardMobile
                  key={item.id}
                  item={item}
                  setDocumentForEditing={setDocumentForEditing}
                  setWarningModalAction={setWarningModalAction}
                  setOpenConfirmDeletion={setOpenConfirmDeletion}
                  documentType={documentType}
                />
              );
            })}
          </Grid>
        )}
      </Box>

      {!loading && dataToShow && (
        <Grid
          item
          sx={{
            position: "fixed",
            bottom: 0,
            width: "100%",
            backgroundColor: "white",
            zIndex: 2,
          }}
        >
          <Pagination
            className={classes.pagination}
            count={pageCount}
            disabled={false}
            paginationLimit={DEFAULT_PAGINATION_LIMIT}
            hideLimitSelector
            hideGoTo
          />
        </Grid>
      )}
    </Grid>
  );
};

const useStyles = makeStyles<Theme>(theme => ({
  card: {
    borderBottom: `1px solid ${theme.palette.specialColors.grey[200]}`,
    alignItems: "center",
    justifyContent: "space-between",
  },
  download: {
    padding: 0,
  },
  pagination: {
    padding: theme.spacing(1, 0, 2, 2),
  },
  loading: {
    padding: theme.spacing(2),
    borderBottom: `1px solid ${theme.palette.specialColors.grey[200]}`,
    display: "flex",
    justifyContent: "space-between",
    alignItems: "center",
  },
}));

function LoadingSkeleton({ style }: { style?: CSSProperties }) {
  const classes = useStyles();
  return (
    <div className={classes.loading} style={{ ...style }}>
      <div style={{ width: "100%", marginLeft: 10 }}>
        <Skeleton variant="text" height={16} />
        <Skeleton variant="text" height={16} />
      </div>
      <div style={{ marginLeft: 20 }}>
        <Skeleton variant="text" height={30} width={50} />
      </div>
    </div>
  );
}
