import { Box, Grid } from "@mui/material";
import { makeStyles } from "@mui/styles";
import { useContext } from "react";
import { clsx } from "clsx";
import { useLayoutStyles } from "@rentancy/ui";
import {
  DocumentContext,
  DocumentsMobileComponent,
  DocumentTabs,
  DocumentsTable,
  DocumentsFileSearch,
  DocumentsDayFilter,
  DocumentTypeFilter,
  DocumentsHeader,
  useDeviceScreen,
  useGlobal,
  DocumentPropertyFilter,
  DocumentCreatedDateFilter,
  DocumentStatusFilter,
} from "@rentancy/common";
import { HomePageSearchBox } from "Components/HomePage/HomePageSearchBox";
import { MenuDrawer } from "Components/HomePage/Drawer";

export const Documents = () => {
  const { isDesktop } = useDeviceScreen();
  const layoutClasses = useLayoutStyles();
  const classes = useStyles();
  const { globalState, dispatch } = useGlobal();

  const { selectedTab } = useContext(DocumentContext);

  const showSideBar = (show: boolean) => {
    dispatch({
      type: "SET_SHOW_SIDEBAR",
      value: show,
    });
  };

  return (
    <Box className={clsx(classes.root, layoutClasses.flexColumn)} sx={{ height: "100%" }}>
      {!isDesktop && (
        <>
          <HomePageSearchBox
            className={classes.mobileHeader}
            handleOpenMenu={() => showSideBar(true)}
            rootStyles={{ minHeight: "75px" }}
          />
          <MenuDrawer
            open={globalState.showSidebar}
            handleClose={() => {
              showSideBar(false);
            }}
            activeMenu={"CONTRACTS"}
          />
        </>
      )}
      <DocumentsHeader />
      {isDesktop && (
        <Box className={classes.content}>
          <Box className={clsx(classes.main, layoutClasses.flexFullWrapper)}>
            <Grid container spacing={2} className={classes.filter}>
              <DocumentsFileSearch />
              <DocumentTypeFilter />
              <DocumentStatusFilter />
              <DocumentPropertyFilter />
              {DocumentTabs.EXPIRING === selectedTab ? (
                <DocumentsDayFilter />
              ) : (
                <DocumentCreatedDateFilter />
              )}
            </Grid>
            <DocumentsTable />
          </Box>
        </Box>
      )}
      {!isDesktop && <DocumentsMobileComponent />}
    </Box>
  );
};

const useStyles = makeStyles(theme => ({
  root: {
    background: theme.palette.specialColors.background.default,
    width: "100%",
  },
  content: {
    padding: theme.spacing(2.5),
    flexGrow: 1,
    display: "flex",
    flexDirection: "column",
    maxHeight: `calc(100vh - ${theme.spacing(26)})`,
  },
  main: {
    border: `1px solid ${theme.palette.specialColors.grey[200]}`,
    background: theme.palette.common.white,
    borderRadius: theme.shape.borderRadius,
  },
  filter: {
    padding: theme.spacing(1.25),
    borderBottom: `1px solid ${theme.palette.specialColors.grey[200]}`,
  },
  mobileHeader: {
    position: "relative",
    marginTop: "0 !important",
  },
  mobileTitle: {
    display: "flex",
    justifyContent: "space-between",
    padding: theme.spacing(0.5, 2.5, 2.5),
    borderBottom: `1px solid ${theme.palette.specialColors.grey[400]}`,
  },
}));
