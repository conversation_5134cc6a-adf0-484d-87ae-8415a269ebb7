import { Box, Grid, debounce, TextField } from "@mui/material";
import {
  DEFAULT_DEBOUNCE_TIME,
  PropertyGroupsFilter,
  useQueryString,
  // useUser,
} from "@rentancy/common";
import { makeStyles } from "@mui/styles";
import { useCallback, useMemo } from "react";
import { useTranslation } from "react-i18next";
import { SearchTextField, DateRangeSelect, LimitedAutocomplete } from "@rentancy/ui";
import moment from "moment/moment";
import { STATUS_OPTIONS } from "./const";

interface Props {
  loading: boolean;
}

export const EndOfTenancyFilters = ({ loading }: Props) => {
  const { query, setQuery } = useQueryString<{
    q: string;
    type?: PropertyGroupsFilter | "ALL";
    managerId?: string;
    clientId?: string;
    toEndDate?: string;
    fromEndDate?: string;
    stage?: string;
  }>();

  const { t } = useTranslation();
  const classes = useStyles();

  const stageOptions = Object.keys(STATUS_OPTIONS);

  // const { user } = useUser();

  const handleChange = useCallback(
    (name: string, value?: string | number) => {
      setQuery({ [name]: value, page: 1 });
    },
    [setQuery],
  );

  const handleChangeDebounced = useMemo(() => {
    return debounce(handleChange, 500);
  }, [handleChange]);

  return (
    <Box>
      <Grid container spacing={1.25} className={classes.filterRow}>
        <Grid item container flexDirection={"column"}>
          <SearchTextField
            debounceTime={DEFAULT_DEBOUNCE_TIME}
            fullWidth
            type="text"
            variant="outlined"
            value={query?.q || ""}
            placeholder={t("propertyOrReference")}
            onChange={e => {
              if (loading) {
                return;
              }
              handleChange("q", e.target.value);
            }}
            inputProps={{
              "data-testid": "property-search-input",
            }}
          />
        </Grid>

        <Grid item xs={2}>
          <LimitedAutocomplete
            options={stageOptions}
            renderInput={props => (
              <TextField
                variant="outlined"
                placeholder={t("modules.endOfTenancy.tablecolumn.currentStage")}
                {...props}
                inputProps={{
                  ...props.inputProps,
                  "data-testid": "eot-search-input",
                }}
              />
            )}
            size="small"
            value={query.stage ?? ""}
            renderOption={(props, option) => (
              <li {...props} key={option} data-testid={`eot-search-item-${option}`}>
                {option}
              </li>
            )}
            onChange={(_, value) => handleChange("stage", value)}
            disabled={loading}
          />
        </Grid>
        <Grid item xs={3} sx={{ alignSelf: "flex-end", marginLeft: "auto" }}>
          <DateRangeSelect
            label={t("endDate")}
            value={[query.fromEndDate, query.toEndDate]}
            onChange={(name, value) =>
              handleChangeDebounced(name, value ? moment(value).utc(true).format() : "")
            }
            startFieldName="fromEndDate"
            endFieldName="toEndDate"
          />
        </Grid>
      </Grid>
    </Box>
  );
};

const useStyles = makeStyles({
  subTitle: {
    background: "#FFF",
    pointerEvents: "none",
  },
  filterRow: {
    "& > div": {
      width: "170px",
      "&:first-child": {
        width: "300px",
      },
    },
  },
});
