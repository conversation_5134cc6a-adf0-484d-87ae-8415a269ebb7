import moment from "moment";
import { Box, Divider, Grid, Skeleton, Stack, Typography } from "@mui/material";
import clsx from "clsx";
import type { Currency, Tenancy, TenancySettingsResponse } from "@rentancy/common";
import { FC, useEffect, useMemo } from "react";
import {
  RoleUtils,
  getContractSummaryBalance,
  getRentInvoiceDates,
  useApi,
  usePropertyDetails,
  useUser,
  useXeroConnection,
} from "@rentancy/common";
import { makeStyles } from "@mui/styles";
import { useTranslation } from "react-i18next";

import { AutoJournalSection } from "./AutoJournalSection";
import { DetailItem } from "./Components";
import { EditContractDetailPopup } from "../EditContractDetail/EditContractDetail";
import { InvoiceSection } from "./InvoiceSection";
import {
  getBreakClause,
  getDeposit,
  getDetails,
  getFinancial,
  getLastPayment,
  getNextPayment,
  getPropertyStatus,
  getRentAmount,
  getSummary,
  getSummaryBalance,
} from "./utils";

const InfoSection = ({
  title,
  items,
  isLoading,
}: {
  title: string;
  items: { label: string; value: any; key?: string }[];
  isLoading: boolean;
}) => (
  <Box>
    <Box
      sx={theme => ({
        borderRadius: 1,
        padding: theme.spacing(1.25),
        background: theme.palette.specialColors.grey[100],
      })}
    >
      <Typography fontWeight="bold">{title}</Typography>
    </Box>

    <Box sx={theme => ({ padding: theme.spacing(1.25) })}>
      {items.map(({ label, value, key }) => (
        <DetailItem key={key || label} isLoading={isLoading} label={label} value={value} />
      ))}
    </Box>
  </Box>
);

const TopSectionItem = ({
  label,
  value,
  isLoading,
}: {
  label: string;
  value: string | null;
  isLoading: boolean;
}) => {
  return (
    <Box key={label} textAlign="center" flex={1} padding={1}>
      <Typography variant="caption" color="text.secondary3">
        {label}
      </Typography>
      {isLoading ? (
        <Skeleton variant="text" />
      ) : (
        <Typography variant="subtitle1">{value || "-"}</Typography>
      )}
    </Box>
  );
};

type LeftPanelProps = {
  tenancy: Tenancy;
  isLoading: boolean;
  refetchTenancy: () => void;
  refetchTenancySettings: () => void;
  tenancySettings: TenancySettingsResponse;
};
export const LeftPanel: FC<LeftPanelProps> = ({
  tenancy,
  isLoading,
  refetchTenancy,
  refetchTenancySettings,
  tenancySettings,
}) => {
  const classes = useStyles();
  const { t } = useTranslation();
  const { details: propertyDetails } = usePropertyDetails();
  const { apiCall: getSummaryApi, data: tenancyBalance } = useApi(getContractSummaryBalance);
  const { integrationStatus } = useXeroConnection();
  const { user } = useUser();
  const {
    apiCall: getRentInvoiceDatesApi,
    data: rentInvoiceDates,
    loading: isRentInvoiceLoading,
  } = useApi(getRentInvoiceDates);

  const details = useMemo(() => {
    return getDetails(t, tenancy);
  }, [t, tenancy]);
  const breakClause = useMemo(() => {
    return getBreakClause(t, tenancy);
  }, [t, tenancy]);
  const summary = useMemo(() => {
    return getSummary(t, tenancy);
  }, [t, tenancy]);
  const summaryBalance = useMemo(() => {
    return getSummaryBalance(t, tenancy, tenancyBalance);
  }, [t, tenancy, tenancyBalance]);
  const financial = useMemo(() => {
    return getFinancial(t, tenancy, user?.organisation?.currency as Currency);
  }, [t, tenancy, user?.organisation?.currency]);
  const deposit = useMemo(() => {
    return getDeposit(t, tenancy);
  }, [t, tenancy]);

  const isXeroFailed = integrationStatus.status !== "CONNECTED";
  const summaryDetails = useMemo(() => {
    if (!isXeroFailed) {
      return [...summary, ...summaryBalance];
    }
    return summary;
  }, [isXeroFailed, summary, summaryBalance]);

  const isInvoiceAvailable =
    RoleUtils.isAgent(user) && integrationStatus.status !== "NOT_CONNECTED";

  const isAutoJournalAvailable = isInvoiceAvailable && !!tenancy?.organisation?.enableJournal;

  useEffect(() => {
    if (!tenancy?.startDate) {
      return;
    }

    getRentInvoiceDatesApi({
      tenancyId: tenancy?.id,
      referenceDate: moment().format("YYYY-MM-DD"),
      prefix: process.env.INTEGRATION_REST_ENDPOINT || "",
    });
  }, [tenancy?.id, getRentInvoiceDatesApi]);

  useEffect(() => {
    if (!tenancy?.id) {
      return;
    }
    getSummaryApi({
      tenancyId: tenancy?.id,
      urlPrefix: process.env.INTEGRATION_REST_ENDPOINT || "",
    });
  }, [tenancy?.id, getSummaryApi]);

  return (
    <Box className={classes.container}>
      <Box display="flex" alignItems="center">
        <Typography variant="h5">{t("currentTenancy")}</Typography>
        <Typography
          variant="body1"
          className={clsx(classes.labelContainer, {
            [classes.grayContainer]: tenancy?.status === "EXPIRING",
          })}
        >
          {tenancy?.status}
        </Typography>
        <div style={{ flex: 1 }} />
        {RoleUtils.isAgent(user) ? (
          <EditContractDetailPopup
            contract={tenancy}
            tenancySettings={tenancySettings}
            onSuccess={() => {
              refetchTenancy();
              refetchTenancySettings();
            }}
          />
        ) : null}
      </Box>

      <Stack
        direction="row"
        alignItems="center"
        justifyContent="center"
        divider={<Divider orientation="vertical" flexItem />}
        spacing={2}
        className={classes.topSection}
      >
        <TopSectionItem
          {...getPropertyStatus(t, propertyDetails?.status || "NA")}
          isLoading={isLoading}
        />
        <TopSectionItem {...getRentAmount(t, tenancy)} isLoading={isLoading} />
        <TopSectionItem {...getNextPayment(t, tenancy)} isLoading={isLoading} />
        <TopSectionItem
          {...getLastPayment(t, rentInvoiceDates)}
          isLoading={isRentInvoiceLoading || isLoading}
        />
      </Stack>

      <Grid display="flex">
        <Grid item xs={12} md={6} marginRight={1.25}>
          <InfoSection title={t("details")} items={details} isLoading={isLoading} />
          <InfoSection title={t("breakClause")} items={breakClause} isLoading={isLoading} />
          <InfoSection title={t("summary")} items={summaryDetails} isLoading={isLoading} />
          {isInvoiceAvailable ? (
            <InvoiceSection
              tenancy={tenancy}
              isLoading={isLoading}
              refetchTenancy={refetchTenancy}
              rentInvoiceDates={rentInvoiceDates}
            />
          ) : null}
        </Grid>
        <Grid item xs={12} md={6} marginLeft={1.25}>
          <InfoSection title={t("financial")} items={financial} isLoading={isLoading} />
          <InfoSection title={t("deposit")} items={deposit} isLoading={isLoading} />
          {isAutoJournalAvailable ? (
            <AutoJournalSection
              tenancy={tenancy}
              isLoading={isLoading}
              refetchTenancy={refetchTenancy}
            />
          ) : null}
        </Grid>
      </Grid>
    </Box>
  );
};

const useStyles = makeStyles(theme => ({
  container: {
    padding: theme.spacing(2.5),
    borderRadius: theme.shape.borderRadius,
    background: theme.palette.common.white,
  },

  topSection: {
    border: `1px solid ${theme.palette.specialColors.themeBorder}`,
    borderRadius: "5px",
    padding: theme.spacing(2.5),
    margin: theme.spacing(0.5, 0, 2.5, 0),
  },
  topSectionTitle: {
    color: theme.palette.specialColors.grey[800],
  },
  labelContainer: {
    marginLeft: theme.spacing(0.75),
    border: `1px solid ${theme.palette.secondary.main}`,
    borderRadius: theme.spacing(2.75),
    padding: theme.spacing(0, 1, 0, 1),
    color: theme.palette.secondary.main,
  },
  grayContainer: {
    border: `1px solid ${theme.palette.text.secondary3}`,
    color: theme.palette.text.secondary3,
  },
}));
